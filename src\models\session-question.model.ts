import { Table } from '@lcs/mssql-utility'
import { EvalSessionQuestion, SessionQuestionFields, SessionQuestionsTableName } from '@tess-f/sql-tables/dist/evaluations/session-questions.js'

// Extended type to include section information retrieved from joins
export type SessionQuestionWithSection = EvalSessionQuestion & {
  SectionId?: string
  SectionVersion?: number
  SectionDisplayIndex?: number
  DisplayIndexWithinSection?: number
  SectionTitle?: string
}

export class SessionQuestionModel extends Table<SessionQuestionWithSection, EvalSessionQuestion> {
  fields: SessionQuestionWithSection

  
  constructor (fields?: SessionQuestionWithSection, record?: SessionQuestionWithSection) {
    super(
      SessionQuestionsTableName,
      [
        SessionQuestionFields.SessionId,
        SessionQuestionFields.QuestionId,
        SessionQuestionFields.QuestionVersion
      ]
    )

    this.fields = fields ?? {}
    if (record) this.importFromDatabase(record)
  }

  importFromDatabase (record: SessionQuestionWithSection): void {
    this.fields = {
      SessionId: record.SessionId,
      QuestionId: record.QuestionId,
      QuestionVersion: record.QuestionVersion,
      PresentationIndex: record.PresentationIndex,
      QuestionSetId: record.QuestionSetId,
      QuestionSetVersion: record.QuestionSetVersion,
      SectionId: record.SectionId,
      SectionVersion: record.SectionVersion,
      SectionDisplayIndex: record.SectionDisplayIndex,
      DisplayIndexWithinSection: record.DisplayIndexWithinSection,
      SectionTitle: record.SectionTitle
    }
  }

  exportJsonToDatabase (): EvalSessionQuestion {
    return {
      SessionId: this.fields.SessionId,
      QuestionId: this.fields.QuestionId,
      QuestionVersion: this.fields.QuestionVersion,
      PresentationIndex: this.fields.PresentationIndex,
      QuestionSetId: this.fields.QuestionSetId,
      QuestionSetVersion: this.fields.QuestionSetVersion
    }
  }
}
