import { Request, Response } from 'express'
import logger from '@lcs/logger'
import { getErrorMessage } from '@tess-f/backend-utils'
import { EventType } from '@tess-f/shared-config'
import submitAssessment from '../../../services/internal/submit-assessment-service.js'
import { SubmitAssessmentRequest } from '../../../models/internal/submit-assessment.js'

const log = logger.create('Controller-HTTP.Submit-Assessment')

/**
 * HTTP controller for submitting completed assessments.
 * 
 * Handles POST requests to submit student assessment responses.
 * Validates the request, processes the submission, and returns results.
 * 
 * Expected request body:
 * - sessionId: string - The assessment session ID
 * - responses: AssessmentQuestionResponse[] - Array of student responses
 * - startTime: string (ISO date) - When the student started the assessment
 * - endTime: string (ISO date) - When the student completed the assessment
 * - notes?: string - Optional notes from the student
 * 
 * Returns:
 * - 200: Successful submission with scoring results
 * - 400: Invalid request data
 * - 500: Server error during processing
 */
export default async function submitAssessmentController(
  req: Request,
  res: Response
): Promise<void> {
  const requestId = req.headers['x-request-id'] || 'unknown'
  
  try {
    log('info', 'Received assessment submission request', {
      requestId,
      sessionId: req.body?.sessionId,
      responsesCount: req.body?.responses?.length || 0,
      eventType: EventType.evaluation_create
    })

    // Extract request data
    const submissionRequest: SubmitAssessmentRequest = {
      sessionId: req.body.sessionId,
      responses: req.body.responses || [],
      startTime: req.body.startTime,
      endTime: req.body.endTime,
      notes: req.body.notes
    }

    // Process the submission
    const result = await submitAssessment(submissionRequest)

    log('info', 'Assessment submission completed successfully', {
      requestId,
      sessionId: result.sessionId,
      passed: result.passed,
      totalScore: result.totalScore,
      maxScore: result.maxScore,
      responsesCount: result.responsesCount,
      eventType: EventType.evaluation_create
    })

    // Return success response
    res.status(200).json({
      success: true,
      data: result,
      message: 'Assessment submitted successfully'
    })

  } catch (error) {
    const errorMessage = getErrorMessage(error)
    
    log('error', 'Failed to submit assessment', {
      requestId,
      sessionId: req.body?.sessionId,
      error: errorMessage,
      eventType: EventType.evaluation_create
    })

    // Determine error status code
    let statusCode = 500
    if (errorMessage.includes('Invalid request data')) {
      statusCode = 400
    } else if (errorMessage.includes('not found')) {
      statusCode = 404
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      message: 'Failed to submit assessment'
    })
  }
}
