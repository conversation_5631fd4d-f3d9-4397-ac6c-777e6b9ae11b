import { Request, Response } from 'express'
import { expect } from 'chai'
import { jest } from '@jest/globals'
import submitAssessmentController from './submit-assessment.controller.js'
import submitAssessment from '../../../services/internal/submit-assessment-service.js'

// Mock the service
jest.mock('../../../services/internal/submit-assessment-service.js')
const mockSubmitAssessment = submitAssessment as jest.MockedFunction<typeof submitAssessment>

describe('submitAssessmentController', () => {
  let mockRequest: Partial<Request>
  let mockResponse: Partial<Response>
  let mockJson: jest.Mock
  let mockStatus: jest.Mock

  beforeEach(() => {
    mockJson = jest.fn()
    mockStatus = jest.fn().mockReturnValue({ json: mockJson })
    
    mockRequest = {
      headers: { 'x-request-id': 'test-request-id' },
      body: {}
    }
    
    mockResponse = {
      status: mockStatus,
      json: mockJson
    }
    
    jest.clearAllMocks()
  })

  describe('successful submission', () => {
    it('should return 200 with submission results', async () => {
      // Arrange
      const mockSubmissionResult = {
        sessionId: 'test-session-id',
        passed: true,
        totalScore: 85,
        maxScore: 100,
        responsesCount: 10,
        gradedQuestionsCount: 10,
        pendingQuestionsCount: 0
      }

      mockRequest.body = {
        sessionId: 'test-session-id',
        responses: [
          {
            QuestionId: 'q1',
            QuestionVersion: 1,
            OptionId: 'opt1',
            Duration: 30000
          }
        ],
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T10:30:00Z'
      }

      mockSubmitAssessment.mockResolvedValue(mockSubmissionResult)

      // Act
      await submitAssessmentController(mockRequest as Request, mockResponse as Response)

      // Assert
      expect(mockSubmitAssessment).toHaveBeenCalledWith({
        sessionId: 'test-session-id',
        responses: mockRequest.body.responses,
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T10:30:00Z',
        notes: undefined
      })

      expect(mockStatus).toHaveBeenCalledWith(200)
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        data: mockSubmissionResult,
        message: 'Assessment submitted successfully'
      })
    })

    it('should handle optional notes in request', async () => {
      // Arrange
      const mockSubmissionResult = {
        sessionId: 'test-session-id',
        passed: false,
        totalScore: 60,
        maxScore: 100,
        responsesCount: 5,
        gradedQuestionsCount: 5,
        pendingQuestionsCount: 0
      }

      mockRequest.body = {
        sessionId: 'test-session-id',
        responses: [],
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T10:30:00Z',
        notes: 'Student had technical difficulties'
      }

      mockSubmitAssessment.mockResolvedValue(mockSubmissionResult)

      // Act
      await submitAssessmentController(mockRequest as Request, mockResponse as Response)

      // Assert
      expect(mockSubmitAssessment).toHaveBeenCalledWith({
        sessionId: 'test-session-id',
        responses: [],
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T10:30:00Z',
        notes: 'Student had technical difficulties'
      })

      expect(mockStatus).toHaveBeenCalledWith(200)
    })
  })

  describe('error handling', () => {
    it('should return 400 for validation errors', async () => {
      // Arrange
      mockRequest.body = {
        sessionId: 'test-session-id',
        responses: []
      }

      mockSubmitAssessment.mockRejectedValue(new Error('Invalid request data: endTime is required'))

      // Act
      await submitAssessmentController(mockRequest as Request, mockResponse as Response)

      // Assert
      expect(mockStatus).toHaveBeenCalledWith(400)
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: 'Invalid request data: endTime is required',
        message: 'Failed to submit assessment'
      })
    })

    it('should return 404 for not found errors', async () => {
      // Arrange
      mockRequest.body = {
        sessionId: 'non-existent-session',
        responses: [],
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T10:30:00Z'
      }

      mockSubmitAssessment.mockRejectedValue(new Error('Session not found'))

      // Act
      await submitAssessmentController(mockRequest as Request, mockResponse as Response)

      // Assert
      expect(mockStatus).toHaveBeenCalledWith(404)
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: 'Session not found',
        message: 'Failed to submit assessment'
      })
    })

    it('should return 500 for general server errors', async () => {
      // Arrange
      mockRequest.body = {
        sessionId: 'test-session-id',
        responses: [],
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T10:30:00Z'
      }

      mockSubmitAssessment.mockRejectedValue(new Error('Database connection failed'))

      // Act
      await submitAssessmentController(mockRequest as Request, mockResponse as Response)

      // Assert
      expect(mockStatus).toHaveBeenCalledWith(500)
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: 'Database connection failed',
        message: 'Failed to submit assessment'
      })
    })
  })
})
